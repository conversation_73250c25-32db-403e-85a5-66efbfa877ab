//
//  ContentView.swift
//  Practice
//
//  Created by <PERSON><PERSON><PERSON>  on 05/06/25.
//

import SwiftUI

struct ContentView: View {
    @State var progress: Float = 0.1
    
    var body: some View {
        VStack {
            Circle()
                .trim(from: 0, to: CGFloat(progress)) // Show 75% of the circle
                .stroke(Color.green, style: StrokeStyle(lineWidth: 100, lineCap: .round))
                .rotationEffect(.degrees(-90))
                .onTapGesture {
                    progress+=0.1
                }
            
        }
    
        .padding()
    }


}

#Preview {
    ContentView()
}
