//
//  ContentView.swift
//  Practice
//
//  Created by <PERSON><PERSON><PERSON>  on 05/06/25.
//

import SwiftUI
import Foundation

struct ContentView: View {
    @State var progress: Float = 0.1
    @State private var pythonOutput: String = ""
    @State private var isRunning: Bool = false

    var body: some View {
        VStack(spacing: 20) {
            Circle()
                .trim(from: 0, to: CGFloat(progress)) // Show 75% of the circle
                .stroke(Color.green, style: StrokeStyle(lineWidth: 100, lineCap: .round))
                .rotationEffect(.degrees(-90))
                .onTapGesture {
                    progress+=0.1
                }

            Button(action: {
                runPythonScript()
            }) {
                HStack {
                    if isRunning {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                    Text(isRunning ? "Running..." : "Run Python Script")
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            .disabled(isRunning)

            if !pythonOutput.isEmpty {
                VStack(alignment: .leading) {
                    Text("Python Output:")
                        .font(.headline)
                    ScrollView {
                        Text(pythonOutput)
                            .font(.system(.body, design: .monospaced))
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }
                    .frame(maxHeight: 200)
                }
            }
        }
        .padding()
    }

    private func runPythonScript() {
        isRunning = true
        pythonOutput = ""

        DispatchQueue.global(qos: .background).async {
            let result = self.executePythonScript()

            DispatchQueue.main.async {
                self.pythonOutput = result
                self.isRunning = false
            }
        }
    }

    private func executePythonScript() -> String {
        let process = Process()
        let pipe = Pipe()
        let errorPipe = Pipe()

        // Set up the process for macOS
        process.executableURL = URL(fileURLWithPath: "/usr/bin/python3")

        // Try to find the Python script in the bundle first, then in project directory
        var scriptPath: String?

        if let bundlePath = Bundle.main.path(forResource: "hello_world", ofType: "py") {
            scriptPath = bundlePath
        } else {
            // For macOS development, try the project directory
            let projectPath = FileManager.default.currentDirectoryPath
            let developmentPath = "\(projectPath)/Practice/hello_world.py"

            if FileManager.default.fileExists(atPath: developmentPath) {
                scriptPath = developmentPath
            }
        }

        guard let finalScriptPath = scriptPath else {
            return "Error: Python script 'hello_world.py' not found.\nMake sure the script is in the app bundle or project directory.\nAlso ensure Python 3 is installed on your system."
        }

        process.arguments = [finalScriptPath]
        process.standardOutput = pipe
        process.standardError = errorPipe

        do {
            try process.run()
            process.waitUntilExit()

            let outputData = pipe.fileHandleForReading.readDataToEndOfFile()
            let errorData = errorPipe.fileHandleForReading.readDataToEndOfFile()

            let output = String(data: outputData, encoding: .utf8) ?? ""
            let errorOutput = String(data: errorData, encoding: .utf8) ?? ""

            if process.terminationStatus == 0 {
                let result = output.trimmingCharacters(in: .whitespacesAndNewlines)
                return result.isEmpty ? "Script executed successfully (no output)" : result
            } else {
                let combinedOutput = [output, errorOutput].filter { !$0.isEmpty }.joined(separator: "\n")
                return "Error: Script failed with exit code \(process.terminationStatus)\n\(combinedOutput)"
            }
        } catch {
            return "Error executing script: \(error.localizedDescription)\n\nTroubleshooting:\n- Make sure Python 3 is installed\n- Check if /usr/bin/python3 exists\n- Verify script permissions"
        }
    }
}

#Preview {
    ContentView()
}
